package com.ecco.security.acl;

import java.io.Serializable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.acls.AclPermissionEvaluator;
import org.springframework.security.acls.model.AclService;
import org.springframework.security.core.Authentication;

// we don't extend AclPermissionEvaluator currently as there are problems with the creation of the constructor bean - some ordering issue
public class CustomPermissionEvaluator implements PermissionEvaluator, ApplicationContextAware, ApplicationListener<ContextRefreshedEvent> {

    private final Logger log = LoggerFactory.getLogger(getClass());

    ApplicationContext applicationContext;
    boolean enableAcls = false;

    AclPermissionEvaluator aclPermissionEvaluator = null;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        checkBeanLoaded();
    }

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {

        // for want of an easier test (eg by profile) this gets the job done
        if (enableAcls)
            return aclPermissionEvaluator.hasPermission(authentication, targetDomainObject, permission);
        else
            // otherwise we allow all
            return true;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType,
            Object permission) {

        if (enableAcls)
            return aclPermissionEvaluator.hasPermission(authentication, targetId, permission);
        else
            // otherwise we allow all
            return true;
    }

    private void checkBeanLoaded() {
        this.enableAcls = (Boolean) applicationContext.getBean("enableAcls");
        if (enableAcls) {
            log.info("ACLs ENABLED");
        } else {
            log.info("ACLs DISABLED");
        }

        if (enableAcls) {
            org.springframework.security.acls.model.AclService aclService = applicationContext.getBean("mutableAclService", AclService.class);
            aclPermissionEvaluator = new AclPermissionEvaluator(aclService);
        }
    }

}
