import $ = require("jquery");
import DateTimePicker = require("../../controls/DateTimePicker");
import {EccoDate} from "@eccosolutions/ecco-common";

import Form = require("../../controls/Form");
import HtmlElement = require("../../controls/HtmlElement");
import Element = require("../../controls/Element");
import ElementContainer = require("../../controls/ElementContainer");
import ActionButton = require("../../controls/ActionButton");
import SelectList = require("../../controls/SelectList");
import Checkbox = require("../../controls/Checkbox");
import EntityRestrictions = require("../../entity-restrictions/EntityRestrictions");
import ReportCriteriaDtoImpl = require("../ReportCriteriaDtoImpl");
import * as spDto from "ecco-dto/service-config-dto";

import NameValue = require("../../common/NameValue");

/**
 * The class which makes the form of the criteria control
 */
class ReportCriteriaComponent implements Element {

    private $container = $("<div>");
    private form = new Form();
    private footer = new HtmlElement($("<span>"));

    private reportCriteriaDtoOut: ReportCriteriaDtoImpl;

    constructor(private reportCriteriaDtoIn: ReportCriteriaDtoImpl, private servicesDto: spDto.ServiceDto[], private onSubmit: (criteria: ReportCriteriaDtoImpl) => void) {
        this.layoutForm();
        this.populateFooter();
    }

    private layoutForm() {
        this.reportCriteriaDtoOut = this.reportCriteriaDtoIn.clone();

        this.form.addClass("form-horizontal");
        this.form.element().attr("role", "form");

        this.layoutFrom();

        this.layoutTo();

        this.layoutRestrictions();

        this.layoutReferralStatus();

        this.layoutNewReferralsOnly();

        this.$container.append(this.form.element());

        return this.$container;
    }

    private layoutFrom() {
        // from
        var fromDteStr = this.reportCriteriaDtoOut.from;
        var initFromDate = EccoDate.parseIso8601(fromDteStr).toLocalJsDate();
        // from - date control
        var fromDateDisplayId = "fromDateDisplay";
        var fromCtl = new DateTimePicker(initFromDate, (source, datetime) => {
            this.reportCriteriaDtoOut.from = EccoDate.fromLocalJsDate(datetime).formatIso8601();
            $("#"+fromDateDisplayId).text(this.reportCriteriaDtoOut.from);
        });
        var $datePickerFrom = $("<input>")
            .attr("id", "datefrom")
            .attr("type", "hidden");
        // from - bootstrap input line
        var $inputDisplayFrom = $("<span>").attr("id", fromDateDisplayId).text(fromDteStr);
        var $inputButtonFrom = $("<span>").append($datePickerFrom);
        var $inputFrom = $("<span>").append($inputDisplayFrom).append($inputButtonFrom);
        this.form.append(this.buildFormRow("from", $inputFrom));
        fromCtl.attach($datePickerFrom);
    }

    private layoutTo() {

        // to
        var toDteStr = this.reportCriteriaDtoOut.to;
        var initToDate = EccoDate.parseIso8601(toDteStr).toLocalJsDate();
        // from - date control
        var toDateDisplayId = "toDateDisplay";
        var toCtl = new DateTimePicker(initToDate, (source, datetime) => {
            this.reportCriteriaDtoOut.to = EccoDate.fromLocalJsDate(datetime).formatIso8601();
            $("#"+toDateDisplayId).text(this.reportCriteriaDtoOut.to);
        });
        var $datePickerTo = $("<input>")
            .attr("id", "dateto")
            .attr("type", "hidden");
        // from - bootstrap input line
        var $inputDisplayTo = $("<span>").attr("id", toDateDisplayId).text(toDteStr);
        var $inputButtonTo = $("<span>").append($datePickerTo);
        var $inputTo = $("<span>").append($inputDisplayTo).append($inputButtonTo);
        this.form.append(this.buildFormRow("to", $inputTo));
        toCtl.attach($datePickerTo);
    }

    private layoutRestrictions() {
        var $rowRestr = new ElementContainer().addClass("e-row");
        $rowRestr.element().attr("data-initial-value-service", this.reportCriteriaDtoOut.serviceId);
        $rowRestr.element().attr("data-initial-value-project", this.reportCriteriaDtoOut.projectId);
        // setup the services/projects drop downs - could add more eg contracts etc
        // but drilling down into filters is less relevant for dashboard reports as its an overview of such things
        var restrictions = new EntityRestrictions(null, null,
                (source) => {
                    this.reportCriteriaDtoOut.serviceId = source.getSelectedService();
                    this.reportCriteriaDtoOut.projectId = source.getSelectedProject();
                },
                "all");
        restrictions.setServicesProjects(this.servicesDto);
        restrictions.attachTo($rowRestr.element());
        this.form.append($rowRestr);
    }

    private layoutReferralStatus() {
        var referralStatus = new SelectList("referralStatusList", false);
        referralStatus.change((value: string) => {
                this.reportCriteriaDtoOut.referralStatus = value;
            });
        var rsLst: NameValue<string>[] = [];
        rsLst.push(new NameValue<string>("created", "created"));
        rsLst.push(new NameValue<string>("received", "received"));
        rsLst.push(new NameValue<string>("ongoing", "ongoing"));
        rsLst.push(new NameValue<string>("live", "live"));
        rsLst.push(new NameValue<string>("incomplete", "incomplete"));
        rsLst.push(new NameValue<string>("waiting", "waiting"));
        rsLst.push(new NameValue<string>("signposted", "signposted (all)"));
        rsLst.push(new NameValue<string>("signposted-referral", "signposted (at referral)"));
        rsLst.push(new NameValue<string>("signposted-service", "signposted (at service)"));
        rsLst.push(new NameValue<string>("exited", "exited"));
        referralStatus.populateFromList(rsLst,
            (item) => ({key: item.name(), value: item.value()}),
            (item) => item.name() == this.reportCriteriaDtoOut.referralStatus);
        var $rowStatus = $("<div>").attr("class", "e-row");
        $("<span>").attr("class", "e-label").text("status ").appendTo($rowStatus);
        $("<span>").attr("class", "input").append(referralStatus.element()).appendTo($rowStatus);
        this.form.append($rowStatus);
    }

    private layoutNewReferralsOnly() {
        // new referrals only
        var newReferralsOnly = new Checkbox("newReferralsOnly", (source, id) => {
            this.reportCriteriaDtoOut.newReferralsOnly = source.getCheckedValue();
            }, this.reportCriteriaDtoOut.newReferralsOnly);
        var $rowNewOnly = $("<div>").attr("class", "e-row");
        $("<span>").attr("class", "e-label").text("new referrals only").appendTo($rowNewOnly);
        $("<span>").attr("class", "input").append(newReferralsOnly.element()).appendTo($rowNewOnly);
        this.form.append($rowNewOnly);
    }

    // InputGroup may be helpful to re-use
    private buildFormRow(label: string, $input: $.JQuery): $.JQuery {
        // layout based on http://www.w3schools.com/bootstrap/bootstrap_forms_sizing.asp (shock)
        // also http://getbootstrap.com/css/#forms
        var id = label+"Id";
        var $label = $("<label>").addClass("control-label").addClass("col-sm-5").attr("for", id).text(label);
        $input.attr("id", id).attr("class", "form-control");
        // remove the borer if we aren't really an input box
        if (!$input.find("input[type=text]").length) {
            $input.css("border", "none");
            $input.css("box-shadow", "none");
        }
        var $inputControl = $("<div>").attr("class", "col-sm-3").append($input);
        var $controlGroup = $("<div>").addClass("form-group").append($label).append($inputControl);
        return $controlGroup;
    }

    private populateFooter() {
        this.footer.append(
            new ActionButton("done")
                .addClass("btn btn-primary")
                .clickSynchronous( () => { this.submitForm(); } )
            );
    }

    private submitForm() {
        this.onSubmit(this.reportCriteriaDtoOut);
    }

    public element() {
        return this.form.element();
    }

    public getFooter() {
        return this.footer.element();
    }

}

export = ReportCriteriaComponent;
