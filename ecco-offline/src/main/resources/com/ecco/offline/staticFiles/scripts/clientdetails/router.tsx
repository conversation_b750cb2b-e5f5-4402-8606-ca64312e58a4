import * as React from "react"
import {Route, Switch} from "react-router";
import {RouteManagedPage} from "../components/RouteManagedPage";
import * as ReactDom from "react-dom"
import ImportWizard = require("./components/ImportWizard");


ReactDom.render(<RouteManagedPage>
                    <Route path="/nav/r/clients">
                        <Switch>
                            <Route path="/nav/r/clients/import/:externalSourceName/:externalRef">
                                {({match}) => <ImportWizard params={match.params}/>}
                            </Route>
                        </Switch>
                    </Route>
                </RouteManagedPage>
    , document.getElementById('mountpoint'));


