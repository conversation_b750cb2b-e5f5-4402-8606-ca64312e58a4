import {<PERSON><PERSON>, BloodhoundConfig} from "bloodhound";

import {ChartDefinitionDto, SessionData, SessionDataAjaxRepository} from "ecco-dto";
import {withAuth<PERSON>rror<PERSON>andler} from "ecco-offline-data";
import {ResizeEvent} from "@eccosolutions/ecco-common";
import {ChartAjaxRepository} from "ecco-dto"
import ChartListEntryControl = require("./ChartListEntryControl");
import ListContainerControl = require("../../controls/data/ListContainerControl");
import ReportDefEditor = require("./ReportDefEditor");
import SearchableListControl = require("../../controls/SearchableListControl");
import SearchableListControlOptions = require("../../controls/SearchableListControlOptions");
import {apiClient} from "ecco-components";

const sessionDataRepository = new SessionDataAjaxRepository(apiClient);
const repository = new ChartAjaxRepository(apiClient);

class ChartsListControlOptions implements SearchableListControlOptions<ChartDefinitionDto> {

    /**
     * @param showOnDashboardManager Filter the reports to just those with this flag
     */
    constructor(private showOnDashboardManager = false, private showOnDashboardFile = false) {
    }

    public placeholderText = "enter part of the report name e.g. 'monthly' or 'overdue'";

    /** Create a control for the given item */
    public createControl(item: ChartDefinitionDto): ChartListEntryControl {
        return new ChartListEntryControl(item);
    }

    public loadInner(callback: (items: ChartDefinitionDto[]) => void): void {
        withAuthErrorHandler(
            sessionDataRepository.getSessionData().then(sd => {
                repository.findAllChartDefinitions()
                    .then( items => {

                        const filtered = items
                            .filter(r => this.showOnDashboardManager ? r.showOnDashboardManager == true : true)
                            .filter(r => this.showOnDashboardFile ? r.showOnDashboardFile == true : true)
                            .filter(r => this.relevantPermission(r, sd));
                        callback(filtered);
                        ResizeEvent.bus.fire();
                    })
            })
        );
    }

    public generateKey(item: ChartDefinitionDto) {
        return String(item.uuid);
    }

    public addEntryText = "define new report";
    public addEntryIconClasses = "fa fa-cogs";
    public addEntryIsAdminOnly = true;

    public addNewEntry() {
        ReportDefEditor.showInModal(null);
    }

    public generateSearchConfig(items: ChartDefinitionDto[]) {
        var searchConfig: BloodhoundConfig<ChartDefinitionDto> = {
            datumTokenizer: (chart: ChartDefinitionDto) => {
                var result = new Array<string>();
                result.push.apply(result, Bloodhound.tokenizers.nonword(chart.name));
                if (chart.friendlyName) {
                    result.push.apply(result, Bloodhound.tokenizers.nonword(chart.friendlyName));
                }
                return result;
            },
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: items,
            limit: 20
        };
        return searchConfig;
    }

    private relevantPermission(r: ChartDefinitionDto, sessionData: SessionData) {
        // if the report is restricted, check its one of the users
        if (r.definition?.selectionCriteria?.serviceId) {
            return sessionData.hasRoleAAA() || sessionData.getRestrictedServices()
                    .map(dto => dto.id)
                    .some(id => id == r.definition.selectionCriteria.serviceId);
        }
        return true;
    }
}

class ChartListControl extends SearchableListControl<ChartDefinitionDto> {

    constructor(showOnDashboardManager = false, showOnDashboardReferral = false) {
        super(ListContainerControl, new ChartsListControlOptions(showOnDashboardManager, showOnDashboardReferral));
    }

}
export default ChartListControl;