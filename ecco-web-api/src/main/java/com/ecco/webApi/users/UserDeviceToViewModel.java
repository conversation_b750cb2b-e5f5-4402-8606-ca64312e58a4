package com.ecco.webApi.users;

import com.ecco.infrastructure.web.UriUtils.ExplicitLinkBuilder;
import com.ecco.security.dom.UserDevice;
import com.ecco.webApi.viewModels.UserDeviceViewModel;
import com.google.common.base.Function;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

import java.util.Base64;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

final class UserDeviceToViewModel implements Function<UserDevice, UserDeviceViewModel> {
    @Nonnull
    @Override
    public UserDeviceViewModel apply(@Nullable UserDevice input) {
        UserDeviceViewModel resource = new UserDeviceViewModel();
        if (input == null) {
            return resource;
        }
        resource.guid = input.getGuid().toString();
        resource.valid = input.isValid();
        if (resource.valid) {
            // No point leaking invalid keys out.
            resource.base64Key = Base64.getEncoder().encodeToString(input.getKey());
            resource.cipher = input.getCipher();
            addSelfLink(resource);
        }
        return resource;
    }

    private void addSelfLink(UserDeviceViewModel resource) {
        resource.add(linkToValidUserDevice(resource).withSelfRel());
    }

    public ExplicitLinkBuilder linkToValidUserDevice(UserDeviceViewModel resource) {
        return linkToApi(methodOn(KeyController.class).getValidUserDeviceKey(resource.guid));
    }
}
