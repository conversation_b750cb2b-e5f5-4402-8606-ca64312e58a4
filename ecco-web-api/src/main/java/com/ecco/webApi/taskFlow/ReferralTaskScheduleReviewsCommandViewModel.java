package com.ecco.webApi.taskFlow;


import javax.annotation.Nullable;

import org.joda.time.LocalDate;

import com.ecco.dto.ChangeViewModel;


public class ReferralTaskScheduleReviewsCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_NAME = "scheduleReviews";

    @Nullable
    public String defaultDatesFromSchedule;

    @Nullable
    public ChangeViewModel<LocalDate> customDateChange;


    ReferralTaskScheduleReviewsCommandViewModel() {
        super();
    }

    public ReferralTaskScheduleReviewsCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_NAME, taskHandle);
    }
}
