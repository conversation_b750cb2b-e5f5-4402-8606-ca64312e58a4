package com.ecco.webApi.controllers;

import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController;
import com.ecco.service.security.RunAsTemplate;
import com.ecco.web.EccoMediaTypes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;

/**
 * The Web API equivalent of EntityListDefinitionResolver or EntityTypeDefinitionResolver.
 *
 * Provides a way to look up a JSON Schema for a particular entity type by name. That JSON schema will then give you a
 * link back to a list of instances of resources of that type, etc. This controller maintains a list of all
 * {@link SchemaProvidingController} beans and uses that to provide the lookup facility.
 *
 * TODO: Review to see if this is covered by cross-context support in https://github.com/cambridgeweblab/api-schema
 *
 * @since 20/01/2016
 */
@SuppressWarnings("rawtypes")
@Slf4j
@RestController
@RequestMapping("/$schemas")
public class SchemaController implements ApplicationListener<ContextRefreshedEvent> {
    private final Map<String, SchemaProvidingController> schemaProvidingControllers = new HashMap<>();

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        log.info("Collecting schemas for /$schemas from bean factory: {}", contextRefreshedEvent.getApplicationContext().getDisplayName());
        Collection<SchemaProvidingController> controllers = contextRefreshedEvent.getApplicationContext().getBeansOfType(SchemaProvidingController.class).values();


        new RunAsTemplate("ROLE_ADMINLOGIN", "ROLE_SYSADMIN", "ROLE_ADMINROTA", "ROLE_STAFF",
                "ROLE_INCIDENTS", "ROLE_REPAIRS", "ROLE_MANAGEDVOIDS",
                "ROLE_USER").execute(() -> {
            controllers.forEach(it -> schemaProvidingControllers.put(it.getEntityTypeName(), it));
            return null;
        });

    }

    /**
     * API to return all known schemas as HATEOAS links.
     */
    @GetJson("/")
    public RepresentationModel getEntitySchemaLinks(WebRequest request) {
        RepresentationModel linkResource = new RepresentationModel();

        for (Map.Entry<String, SchemaProvidingController> entry : schemaProvidingControllers.entrySet()) {
            linkResource.add(linkToApi(entry.getValue().self().describe(request)).withRel(entry.getKey()));
        }

        return linkResource;
    }

    /**
     * Analogous to EntityListDefinitionResolver#getEntityListDefinition(String)
     * in a Web API world.
     * Will identify a controller with
     *
     * @param entityTypeName the entity name to lookup
     */
    @GetMapping(value = "/{entityTypeName}", produces = EccoMediaTypes.APPLICATION_SCHEMA_JSON_UTF8_VALUE)
    public ResponseEntity<Void> getEntitySchema(@PathVariable String entityTypeName, WebRequest request) {
        SchemaProvidingController controller = schemaProvidingControllers.get(entityTypeName);
        if (controller != null) {
            return ResponseEntity
                    .status(HttpStatus.SEE_OTHER)
                    .header(HttpHeaders.LOCATION, linkToApi(controller.self().describe(request)).toString())
                    .build();
        } else {
            return ResponseEntity
                    .notFound()
                    .build();
        }
    }
}
