import {mount} from "@cypress/react";
import * as React from "react";
import {EvidencePagePlanLayout, EvidencePageSetupForCommandForm} from "../../EvidencePage";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {sessionData, testEvidencePagePlanData} from "../../test-support/mockEvidence";
import {EccoAPI} from "ecco-components/EccoAPI";
import {CommandForm} from "ecco-components";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {Command} from "ecco-commands";
import {EvidencePageType} from "ecco-dto";

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("EvidencePagePlan tests", () => {
    it("mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/* EvidencePageRoot - directly, when no commandForm required */}
                            {/* EvidencePageLoaderForCommandForm - when loading data */}
                            <EvidencePageSetupForCommandForm
                                initData={{
                                    ...testEvidencePagePlanData(
                                        EvidencePageType.assessmentReduction
                                    ),
                                    readOnly: false
                                }}
                            >
                                <EvidencePagePlanLayout />
                                <CommandFormTestOutput
                                    cmdEmitted={cmdEmitted}
                                    cmdEmittedDraft={cmdEmittedDraft}
                                />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        // breakpoints: xs, extra-small: 0px ; sm, small: 600px ; md, medium: 900px ; lg, large: 1200px ; xl, extra-large: 1536px.
        // for us, md is at 960
        cy.viewport(1200, 750);
    });
});
