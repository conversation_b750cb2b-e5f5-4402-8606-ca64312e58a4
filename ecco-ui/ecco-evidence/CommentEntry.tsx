import {Grid, TextField} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, useEffect, useState} from "react";
import {CommentEntryProps} from "./CommentEntryRoot";
import {
    datePickerInput,
    dateTimeIso8601Input,
    dropdownList,
    numberInput,
    stringFromHtmlInput
} from "ecco-components";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";

const CommentEntryLayout: FC<CommentEntryProps> = props => {
    const taskName = props.init.initData.evidenceDef.getTaskName();
    const serviceType = props.init.initData.configResolver.getServiceType();

    const hasComment = !props.init.initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(taskName, "showCommentComponents", "!comment");
    const hasCommentType = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showCommentComponents",
        "type"
    );
    const hasMins = props.init.initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(taskName, "showCommentComponents", "minutesSpent");
    const hasClientStatus = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showCommentComponents",
        "clientStatus"
    );
    const hasMeetingStatus = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showCommentComponents",
        "meetingStatus"
    );

    return (
        <Grid container direction="row" justify="center" alignItems="center">
            <Grid item md={8} xs={12}>
                <Grid container spacing={2}>
                    {hasComment && (
                        <Grid item xs={12}>
                            <CommentTextArea {...props} />
                        </Grid>
                    )}
                    <Grid item xs={12} sm={6}>
                        <WorkDate {...props} />
                    </Grid>
                    {hasCommentType && (
                        <Grid item xs={12} sm={6}>
                            <CommentType {...props} />
                        </Grid>
                    )}
                    {hasMins && (
                        <Grid item xs={12} sm={6}>
                            <MinsSpent {...props} />
                        </Grid>
                    )}
                    {hasClientStatus && (
                        <Grid item xs={12} sm={6}>
                            <ClientStatus {...props} />
                        </Grid>
                    )}
                    {hasMeetingStatus && (
                        <Grid item xs={12} sm={6}>
                            <MeetingStatus {...props} />
                        </Grid>
                    )}
                </Grid>
            </Grid>
        </Grid>
    );
};

const WorkDate: FC<CommentEntryProps> = props => {
    const {state, stateSetter} = props;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const serviceType = props.init.initData.configResolver.getServiceType();

    const showStartAsDateTime = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "tookPlaceOn",
        "dateTime"
    );

    // datePickerInput expects to work with an EccoDate,
    // dateTimeIso expects to work with a string
    const workEccoDate = {
        workDate: (state.workDate as EccoDate) || null
    };
    const workEccoDateSetter = (state: {workDate: EccoDate | null}) =>
        stateSetter({workDate: state.workDate || undefined});

    const workStr = {
        workDate: state.workDate?.formatIso8601() || null
    };
    const workStrSetter = (state: {workDate: string | null}) =>
        // ignoring timezone means its treated as local time
        // see EvidenceCommandHandler and convertFromUsersLocalDateTime
        stateSetter({
            workDate: EccoDateTime.parseIso8601IgnoringTimezone(state.workDate) || undefined
        });

    return (
        <>
            {showStartAsDateTime
                ? dateTimeIso8601Input(
                      "workDate",
                      "took place on",
                      workStrSetter,
                      workStr,
                      false,
                      true
                  )
                : datePickerInput(
                      "workDate",
                      "took place on",
                      workEccoDateSetter,
                      workEccoDate,
                      false,
                      true
                  )}
        </>
    );
};

const ClientStatus = (props: CommentEntryProps) => {
    const {state, stateSetter} = props;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const sessionData = props.init.initData.sessionData;
    const serviceType = props.init.initData.configResolver.getServiceType();

    const clientStatusListName = serviceType.getTaskDefinitionSetting(
        taskName,
        "clientStatusListName"
    );
    const clientStatusList = sessionData.getListDefinitionEntriesByListName(
        clientStatusListName!,
        undefined,
        state.clientStatusId
    );
    const list = clientStatusList.map(ld => {
        return {
            id: ld.getId(),
            name: ld.getName(),
            disabled: ld.getDisabled()
        };
    });
    const required = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "validateComment",
        "clientStatus"
    );

    return (
        <>
            {dropdownList(
                "client status",
                stateSetter,
                state,
                "clientStatusId",
                list,
                undefined,
                false,
                required
            )}
        </>
    );
};

const MeetingStatus = (props: CommentEntryProps) => {
    const {state, stateSetter} = props;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const sessionData = props.init.initData.sessionData;
    const serviceType = props.init.initData.configResolver.getServiceType();

    const meetingStatusListName = serviceType.getTaskDefinitionSetting(
        taskName,
        "meetingStatusListName"
    );
    const meetingStatusList = sessionData.getListDefinitionEntriesByListName(
        meetingStatusListName!,
        undefined,
        state.meetingStatusId
    );
    const list = meetingStatusList.map(ld => {
        return {
            id: ld.getId(),
            name: ld.getName(),
            disabled: ld.getDisabled()
        };
    });
    const required = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "validateComment",
        "meetingStatus"
    );

    return (
        <>
            {dropdownList(
                "meeting status",
                stateSetter,
                state,
                "meetingStatusId",
                list,
                undefined,
                false,
                required
            )}
        </>
    );
};

const CommentType = (props: CommentEntryProps) => {
    const {state, stateSetter} = props;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const serviceType = props.init.initData.configResolver.getServiceType();
    const types = props.init.initData.commentTypes.filter(
        t => !t.disabled || t.id == state.commentTypeId
    );
    const required = serviceType.taskDefinitionSettingHasFlag(taskName, "validateComment", "type");

    const typeLabel =
        props.init.initData.configResolver
            .getServiceType()
            .getTaskDefinitionSetting(taskName, "typeLabel") || "type";

    return (
        <>
            {dropdownList(
                typeLabel,
                stateSetter,
                state,
                "commentTypeId",
                types,
                undefined,
                false,
                required
            )}
        </>
    );
};

const CommentTextArea = (props: CommentEntryProps) => {
    const {state, stateSetter} = props;
    const [comment, setComment] = useState(state.comment);
    const taskName = props.init.initData.evidenceDef.getTaskName();

    const commentLabel =
        props.init.initData.configResolver
            .getServiceType()
            .getTaskDefinitionSetting(taskName, "commentLabel") || "comment";

    useEffect(() => {
        stateSetter({comment: comment});
    }, [comment]);

    const nullComment = props.init.initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(taskName, "validateComment", "allowNullComment");
    return (
        <TextField
            name={`evidence-comment`}
            label={commentLabel}
            /*type={type}*/
            fullWidth={true}
            /*size={"medium"}*/
            /*placeholder={"goal plan..."}*/
            /*disabled={options.disabled}*/
            /*rowsMax={options.rows}*/
            required={!nullComment}
            /*error={valid == "error"}*/
            multiline={true}
            /*minRows={3}*/
            rows={3}
            onChange={event => setComment(stringFromHtmlInput(event.target) || "")}
            value={comment}
        />
    );
    /*return <>{textArea("comment", "comment", stateSetter, state, undefined, false)}</>;*/
};

const MinsSpent = (props: CommentEntryProps) => {
    const {state, stateSetter} = props;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const hasMins = props.init.initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(taskName, "showCommentComponents", "minutesSpent");
    if (!hasMins) {
        return null;
    }

    const minsLabel = "mins spent";
    /*props.init.initData.configResolver
        .getServiceType()
        .getTaskDefinitionSetting(taskName, "minutesSpentLabel") || "mins spent";*/

    const validate = props.init.initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(taskName, "validateComment", "minsSpent");
    return (
        <>
            {numberInput(
                "minsSpent",
                minsLabel,
                stateSetter,
                state,
                undefined,
                undefined,
                undefined,
                undefined,
                validate
            )}
        </>
    );
};

/**
 * External entry point to the layout
 */
export const CommentEntry: FC<CommentEntryProps> = props => {
    return <CommentEntryLayout {...props} />;
};
