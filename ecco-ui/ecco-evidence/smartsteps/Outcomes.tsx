import * as React from "react";
import {FC, useCallback, useState} from "react";
import {stringToColorRGB, TabsBuilder, useServicesContext} from "ecco-components";
import {IconMenu, MenuIcon} from "@eccosolutions/ecco-mui-controls";
import {useEvidencePageContext} from "../EvidencePageRoot";
import {SmartStepState, SmartStepWrapper} from "./SmartStepRoot";
import {EvidencePageType, Outcome, SessionData, SmartStepDisplaySymbol} from "ecco-dto";
import {Box, Grid, MenuItem, Typography} from "@eccosolutions/ecco-mui";
import {QuestionAnswerWrapper} from "../questionanswers/QuestionAnswerRoot";
import {SmartStepStatusSymbol} from "./SmartStep";

/**
 * Get the outcomes from the SmartStepState, since this represents everything we want to show - its created
 * from snapshot and/or reference data (see createSmartStepInits)
 */
export function getOutcomeTabs(
    smartSteps: SmartStepState[],
    sessionData: SessionData,
    allowQuestionAnswer: boolean
) {
    const outcomeIds = smartSteps
        .map(s =>
            s.actionDefId
                ? sessionData.getAnyActionById(s.actionDefId).getOutcome().getId()
                : s.transientOutcomeDefId &&
                  sessionData.getAnyOutcomeById(s.transientOutcomeDefId)?.id
        )
        .filter((id): id is number => !!id);
    const uniqueIds = [...new Set(outcomeIds)];

    const tabs = new TabsBuilder();
    uniqueIds.map(id => {
        tabs.addTab(
            sessionData.getOutcomeById(id)?.getName() || "unknown",
            <OutcomeSectionLayout
                outcomeId={id}
                key={`${id}-outcomelayout`}
                allowQuestionAnswer={allowQuestionAnswer}
            />,
            undefined,
            "fa-file-text-o"
        );
    });
    return tabs.build();
}

/**
 * Get the outcomes from the SmartStepState, since this represents everything we want to show - its created
 * from snapshot and/or reference data (see createSmartStepInits)
 */
export function getOutcomeSections(
    outcomes: Outcome[],
    allowQuestionAnswer: boolean
) {
    const {sessionData} = useServicesContext();
    const {init, state, dispatch} = useEvidencePageContext();

    // NB avoid the hard coded EvidencePageType - initData.evidenceDef.getEvidencePageType() == EvidencePageType.reduction;
    const alwaysHideAddSmartStep = init.initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(
            init.initData.evidenceDef.getTaskName(),
            "actAs",
            "reduction"
        );

    const addSmartStep = useCallback((transientOutcomeId: number) => {
        dispatch({
            type: "addSmartStep",
            initData: init.initData,
            transientOutcomeDefId: transientOutcomeId
        });
    }, []);

    const addNewSmartStep = state.pageEditing && !alwaysHideAddSmartStep;

    return (
        <Grid container key={"outcome-sections"}>
            {outcomes.map(outcome => {
                const outcomeId = outcome.getId();
                const emptyOutcome =
                    smartStepsForOutcome(sessionData, state.smartSteps, outcomeId).length == 0;
                const colour = stringToColorRGB(outcome.getName() || outcomeId.toString()); //sessionData.getOutcomeById(id)?.getParams().getColour();
                return (
                    <>
                        <Grid
                            item
                            xs={12}
                            md={2}
                            key={`${outcomeId}-outcome-name`}
                            style={{
                                backgroundColor: `rgba(${colour.r}, ${colour.g}, ${colour.b}, 0.2)`
                            }}
                            container
                            direction="row"
                            justify="center"
                            alignItems="center"
                        >
                            <Typography>{outcome.getName() || "unknown"}</Typography>
                        </Grid>
                        <Grid item xs={12} md={10}>
                            <OutcomeSection
                                outcomeId={outcomeId}
                                key={`${outcomeId}-outcome`}
                                allowQuestionAnswerMenuItem={allowQuestionAnswer}
                            />
                            {/* as per SmartStepViewLayout */}
                            {!addNewSmartStep && emptyOutcome && (
                                <Box border={1} padding={1} borderColor={"#ddd"} borderRadius={8}>
                                    <Grid container direction="row">
                                        <Grid container>
                                            <Typography>-</Typography>
                                        </Grid>
                                    </Grid>
                                </Box>
                            )}
                            {addNewSmartStep && (
                                <Box border={1} padding={1} borderColor={"#ddd"} borderRadius={8}>
                                    <Grid container direction="row">
                                        <Grid container>
                                            <SmartStepStatusSymbol
                                                onClick={() => addSmartStep(outcome.getId())}
                                                symbol={SmartStepDisplaySymbol.FullPlus}
                                            />
                                        </Grid>
                                    </Grid>
                                </Box>
                            )}
                        </Grid>
                    </>
                );
            })}
        </Grid>
    );
}

const OutcomeSectionLayout: FC<{outcomeId: number; allowQuestionAnswer: boolean}> = props => {
    return (
        <Grid container>
            <Grid item md={2}>
                &nbsp;
            </Grid>
            <Grid item md={8}>
                <OutcomeSection
                    outcomeId={props.outcomeId}
                    key={`${props.outcomeId}-outcome`}
                    allowQuestionAnswerMenuItem={props.allowQuestionAnswer}
                />
            </Grid>
            <Grid item md={2}>
                &nbsp;
            </Grid>
        </Grid>
    );
};

type IconMenuProps = {
    actionDefId: number;
    onAddQuestionAnswer: () => void;
};
const Menu: FC<IconMenuProps> = props => {
    const [menuOpen, setMenuOpen] = useState(false);

    const onMenuChoice = () => {
        setMenuOpen(false);
        props.onAddQuestionAnswer();
    };

    return (
        <IconMenu
            id={`${props.actionDefId}-menu`}
            iconComponent={<MenuIcon />}
            onClick={() => setMenuOpen(true)}
            open={menuOpen}
            onClose={() => setMenuOpen(false)}
        >
            <MenuItem title={"add question"} onClick={() => onMenuChoice()}>
                add question
            </MenuItem>
        </IconMenu>
    );
};
export const SmartStepMenu: FC<{
    actionDefId: number;
    allowQuestionAnswerMenuItem?: boolean;
}> = props => {
    const {init, dispatch} = useEvidencePageContext();
    const {sessionData} = useServicesContext();

    const relatedQuestionDefId = sessionData.getQuestionDefIdFromActionDefId(
        init.initData.taskName,
        props.actionDefId
    );

    const addSmartStep = useCallback(() => {
        if (relatedQuestionDefId) {
            dispatch({
                type: "addQuestionAnswer",
                initData: init.initData,
                questionDefId: relatedQuestionDefId
            });
        }
    }, []);

    return (
        <>
            {props.allowQuestionAnswerMenuItem && relatedQuestionDefId && (
                <Menu onAddQuestionAnswer={addSmartStep} actionDefId={props.actionDefId} />
            )}
        </>
    );
};

function smartStepsForOutcome(
    sessionData: SessionData,
    smartSteps: SmartStepState[],
    outcomeId: number
) {
    return smartSteps.filter(s =>
        s.actionDefId
            ? sessionData.getAnyActionById(s.actionDefId).getOutcome().getId() == outcomeId
            : s.transientOutcomeDefId == outcomeId
    );
}

const OutcomeSection: FC<{outcomeId: number; allowQuestionAnswerMenuItem: boolean}> = props => {
    const {sessionData} = useServicesContext();
    const {init, state: statePage} = useEvidencePageContext();

    // draw the smart step
    const smartStep = (state: SmartStepState) => {
        const relatedQuestionDefId =
            state.actionDefId &&
            sessionData.getQuestionDefIdFromActionDefId(init.initData.taskName, state.actionDefId);

        // avoid '0' being an option, which redners '0'
        const questionAnswerStates = relatedQuestionDefId
            ? statePage.questionAnswers.filter(s => s.questionDefId == relatedQuestionDefId)
            : null;

        const showMenuToAddQuestionAnswer =
            props.allowQuestionAnswerMenuItem &&
            !!relatedQuestionDefId &&
            (!questionAnswerStates || questionAnswerStates.length == 0);

        return (
            <>
                <SmartStepWrapper
                    state={state}
                    key={`${state.controlUuid}-wrapper`}
                    showMenu={showMenuToAddQuestionAnswer}
                    allowQuestionAnswerMenuItem={props.allowQuestionAnswerMenuItem}
                />
                {questionAnswerStates &&
                    questionAnswerStates.map(q => (
                        <QuestionAnswerWrapper
                            state={q}
                            key={`${q.questionDefId}-wrapper`}
                            showMenu={false}
                            allowSmartStepMenuItem={false}
                        />
                    ))}
            </>
        );
    };

    // relevant to outcomeId
    const smartSteps = smartStepsForOutcome(sessionData, statePage.smartSteps, props.outcomeId);

    // TODO ordering
    const topLevelSmartSteps = smartSteps.filter(s => s.parentControlUuid == null);

    // could be done as props, but we have the context which can be stubbed
    return (
        <>
            {topLevelSmartSteps.map(s => (
                <div key={`${s.controlUuid}-actionid`}>{smartStep(s)}</div>
            ))}
        </>
    );
};
