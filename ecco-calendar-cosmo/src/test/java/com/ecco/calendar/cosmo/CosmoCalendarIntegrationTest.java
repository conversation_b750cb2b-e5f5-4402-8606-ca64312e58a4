package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.*;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.root.*;
import com.ecco.infrastructure.time.Clock;
import com.ecco.test.support.TestAppContextInitializer;
import com.ecco.test.support.UniqueDataService;
import com.google.common.collect.Range;
import com.ecco.calendar.config.CalendarServiceConfig;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.Duration;
import org.joda.time.Interval;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Nonnull;
import java.net.URI;
import java.time.DayOfWeek;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.is;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = TestAppContextInitializer.class,
        classes = {InfrastructureConfig.class,
                CalendarServiceConfig.class,
                CosmoCalendarIntegrationTest.ServiceConfig.class})

@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
public class CosmoCalendarIntegrationTest {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    CosmoCalendarService cosmoCalendarService;

    private final UniqueDataService unique = UniqueDataService.instance;
    private final Clock clock = Clock.DEFAULT;
    protected final org.joda.time.DateTime now = clock.nowWithoutMillies();

    @Test
    public void saveNewRecurringEntry() {

        // WHEN
        // create a calendar and a recurring entry
        // To match the returned events with the recurringEntry (so we can filter other tests out) we ensure
        // we create a new calendar per test. This would be the same as checking the ownerUid of the event - because
        // the owner of the event is the calendar owner (see noteItem.setOwner(calendar.getOwner()).
        // Or, we could specify an managedByUri which would come back for us to filter events on.
        DateTime start = now.plusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY).withHourOfDay(14);
        var answers = createCalendarAndRecurringEntry("saveNewRecurring", start);

        // THEN
        // we have events at 2pm every day for 5 weeks (15 min duration)
        // where we test only a few of those dates (start and end)
        var interval = new Interval(start, start.plusWeeks(10));
        verifyRecurrences(answers.first, interval, 35, start, start.plusWeeks(5).minusDays(1));
    }

    /**
     * Standard test for original functionality.
     * Checks that we can create, part-allocate and get expected results.
     */
    @Test
    public void allocateToRecurringEntry() {

        // GIVEN
        // create a calendar and a recurring entry every day, and some other calendars as attendees
        String calendarId, calendarIdResource;
        String calendarIdResourceUserReferenceUri;
        RecurringEntry recurringEntry;
        String uniqueFor = "allocateToRecurring";
        String uniqueForResource = "allocateToRecurring-resource";
        DateTime start = now.plusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY).withHourOfDay(14);
        {
            calendarId = createCalendar(uniqueFor);
            calendarIdResource = createCalendar(uniqueForResource);
            calendarIdResourceUserReferenceUri = cosmoCalendarService.findCalendarIdUserReferenceUri(calendarIdResource);
            RecurringEntryDefinition.Builder defaultRecurring = constructRecurringEntry(start, uniqueFor);
            recurringEntry = cosmoCalendarService.createRecurringEntry(calendarId, defaultRecurring.build());
        }

        // WHEN
        // allocate across varied appointments on the recurrences
        // events occur next Monday at 2pm for 15 minutes ending in 5 weeks
        // PART-ALLOCATE for Mon,Tues,Wed
        {
            Range<Instant> range = Range.closedOpen(start.toDate().toInstant(), start.plusWeeks(5).toDate().toInstant());
            URI updatedBy = URI.create(String.format("test://%s-%s/", uniqueFor, "MonTuesWed"));
            DaysOfWeek days = dayOfWeek -> List.of(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY).contains(DayOfWeek.of(dayOfWeek));
            // using calendarService prevents UndeclaredThrowableException which comes from Session - because the entities here were no longer in the tx
            // in NoteOccurrenceToRecurrenceAdapter.kt#65 (noteOccurrence.parents)
            cosmoCalendarService.confirmRecurrencesInRange(recurringEntry.getHandle(), range,
                    days, updatedBy, null,  null, calendarIdResource);
        }

        // PART-ALLOCATE for Thurs for the first 2 weeks
        {
            Range<Instant> range = Range.closedOpen(start.toDate().toInstant(), start.plusWeeks(2).toDate().toInstant());
            URI updatedBy = URI.create(String.format("test://%s-%s/", uniqueFor, "Thurs"));
            DaysOfWeek days = dayOfWeek -> Objects.equals(DayOfWeek.THURSDAY, DayOfWeek.of(dayOfWeek));
            cosmoCalendarService.confirmRecurrencesInRange(recurringEntry.getHandle(), range,
                    days, updatedBy, null,  null, calendarIdResource);
        }

        // PART-ALLOCATE for Fri,Sat,Sun for weeks 2 to 4, leaving the last week blank on Thurs,Fri,Sat,Sun
        {
            Range<Instant> range = Range.closedOpen(start.plusWeeks(2).toDate().toInstant(), start.plusWeeks(4).toDate().toInstant());
            URI updatedBy = URI.create(String.format("test://%s-%s/", uniqueFor, "FriSatSun"));
            DaysOfWeek days = dayOfWeek -> List.of(DayOfWeek.FRIDAY, DayOfWeek.SATURDAY, DayOfWeek.SUNDAY).contains(DayOfWeek.of(dayOfWeek));
            cosmoCalendarService.confirmRecurrencesInRange(recurringEntry.getHandle(), range,
                    days, updatedBy, null,  null, calendarIdResource);
        }

        // THEN
        // we have events at 2pm every day for 5 weeks (15 min duration)
        // where we test every one has the same attendee (resource)
        {
            var interval = new Interval(start, start.plusWeeks(10)); // extend the end date to be sure we catch stragglers
            Set<Entry> events = cosmoCalendarService.findEntries(calendarId, interval).getEntries();

            // NB 35 days is 5 weeks, but to get this we need to exclude the last day in the recurring entry
            assertThat(events.size(), is(35));

            // we should see one event, but different updatedBy's
            List<URI> uris = events.stream()
                    .map(Entry::getUpdatedByUri)
                    .collect(Collectors.toList());
            assertThat(uris.stream().filter(Objects::isNull).count(), is(12L)); // 3 thurs missing, 3 weeks of fri,sat,sun missing
            var urisNonNull = uris.stream().filter(Objects::nonNull).collect(Collectors.toList());
            var monTuesWed = String.format("test://%s-%s/", uniqueFor, "MonTuesWed");
            var thurs = String.format("test://%s-%s/", uniqueFor, "Thurs");
            var friSatSun = String.format("test://%s-%s/", uniqueFor, "FriSatSun");
            assertThat(urisNonNull.stream().filter(u -> monTuesWed.equals(u.toString())).count(), is(15L)); // monTuesWed on 5 weeks
            assertThat(urisNonNull.stream().filter(u -> thurs.equals(u.toString())).count(), is(2L)); // thurs on 2 weeks
            assertThat(urisNonNull.stream().filter(u -> friSatSun.equals(u.toString())).count(), is(6L)); // friSatSun on 2 weeks

            // NB events give 2 attendees - because the owner of the calendar might not be an attendee
            // filter only the resource - ideally set only provides 1
            Set<Attendee> attendees = events.stream()
                    .map(Entry::getAttendees)
                    .flatMap(Collection::stream)
                    .filter(a -> calendarIdResourceUserReferenceUri.equals(a.getCalendarIdUserReferenceUri()))
                    .collect(Collectors.toSet());
            assertThat(attendees.size(), is(15+2+6));
        }
    }

    @SuppressWarnings("SameParameterValue")
    private String createCalendar(@Nonnull String uniqueFor) {
        CalendarOwnerDefinition.Builder cal = CalendarOwnerDefinition.BuilderFactory.create()
                .username(unique.userNameFor(uniqueFor))
                .email(unique.userNameFor(uniqueFor).concat("@email.com"))
                .password(unique.passwordFor(uniqueFor))
                .firstName(unique.firstNameFor(uniqueFor))
                .lastName(unique.lastNameFor(uniqueFor));
        return cosmoCalendarService.createCalendar(cal.build());
    }

    @SuppressWarnings({"UnusedReturnValue", "SameParameterValue"})
    private Set<Entry> verifyRecurrences(String calendarId, Interval interval, int counts, DateTime... contains) {
        Set<Entry> events = cosmoCalendarService.findEntries(calendarId, interval).getEntries();
        // NB 35 days is 5 weeks, but to get this we need to exclude the last day in the recurring entry
        assertThat(events.size(), is(counts));
        assertThat(events.stream().map(Entry::getStart).collect(Collectors.toList()), hasItems(contains));
        return events;
    }

    @SuppressWarnings("SameParameterValue")
    private Pair<String, RecurringEntry> createCalendarAndRecurringEntry(String uniqueFor, DateTime start) {
        String calendarId;
        calendarId = createCalendar(uniqueFor);
        cosmoCalendarService.findCalendarIdUserReferenceUri(calendarId);
        RecurringEntryDefinition.Builder defaultRecurring = constructRecurringEntry(start, uniqueFor);
        var entry = cosmoCalendarService.createRecurringEntry(calendarId, defaultRecurring.build());
        return new Pair<>(calendarId, entry);
    }

    /**
     * Construct a basic recurring entry for every day for 5 weeks from next Monday at 2pm (15 min duration)
     */
    @SuppressWarnings("SameParameterValue")
    private RecurringEntryDefinition.Builder constructRecurringEntry(DateTime start, @Nonnull String uniqueFor) {
        Set<Integer> days = new HashSet<>(Arrays.asList(1,2,3,4,5,6,7));
        return RecurringEntryDefinition.BuilderFactory.create()
                .title(uniqueFor)
                //.managedByUri(URI.create("test://"+uniqueFor+"/"))
                .start(start)
                .scheduleEndDate(start.toLocalDate().plusWeeks(5).minusDays(1))
                .duration(Duration.standardMinutes(15))
                .intervalType("WK")
                .calendarDays(days)
                .intervalFrequency(1)
                .location(LocationDefinition.BuilderFactory.create().build());
    }

    @Configuration
    public static class ServiceConfig {

        @Bean
        public LocationSyncAgentTest registerTestSync(MessageBus<ApplicationEvent> messageBus) {
            return new LocationSyncAgentTest(messageBus);
        }

        @Bean
        public SoftwareFeatureService softwareFeatureService() {
            return Mockito.mock(SoftwareFeatureService.class);
        }
    }

    public static class Pair<T, U> {
        public final T first;
        public final U second;

        public Pair(T first, U second) {
            this.first = first;
            this.second = second;
        }
    }
}
