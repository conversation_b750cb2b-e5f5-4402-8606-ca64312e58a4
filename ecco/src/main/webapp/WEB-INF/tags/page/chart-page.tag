<%@tag body-content="scriptless" pageEncoding="UTF-8" trimDirectiveWhitespaces="true" %>

<%@attribute name="title" type="java.lang.String" required="true"
        description="The title of the page." %>
<%@attribute name="importRequireJsModules" type="java.lang.String" required="false"
        description="A space-separated list of require.js modules to import (bootstrap already included)." %>

<%@attribute name="devMode" type="java.lang.Boolean" required="true"
             description="True if this page should use dev resources." %>

<%@taglib prefix="page" tagdir="/WEB-INF/tags/page"%>
<%@taglib prefix="security" uri="http://www.springframework.org/security/tags"  %>

<security:authorize var="roleUser" access="hasAnyRole('ROLE_USER')"/>

<page:rich-client-page isTransitionalUI="${false}" title="${title}"
    importRequireJsModules="bootstrap controls/NetworkActivityIndicator ${importRequireJsModules} ${roleUser ? 'online/timeout' : ''}"
    importHeadFiles="/WEB-INF/views/heads/reportCharts.jsp" devMode="${devMode}">

    <page:main_head_jsbase/>

    <page:page-banner>
        <page:page-menus-bs title="charts" noCollapse="true" quickGuideId="quickGuide"/>
    </page:page-banner>

    <jsp:doBody/>
</page:rich-client-page>