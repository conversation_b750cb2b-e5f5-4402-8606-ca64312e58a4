package com.ecco.webApi.calendar;

import com.ecco.calendar.core.CalendarService;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.hateoas.RepresentationModel;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.net.URI;

@Controller
@CrossOrigin
@RequestMapping("/calendar/availability")
public class AvailabilityController {
    private static final Logger log = LoggerFactory.getLogger(AvailabilityController.class);

    @Autowired
    private CalendarService calendarService;

    @GetJson("/{id}/{from}-{to}")
    @ResponseBody
    public AvailabilityResource getAvailability(@PathVariable String id,
                                                 @PathVariable @DateTimeFormat(pattern = "yyyyMMdd") DateTime from,
                                                 @PathVariable @DateTimeFormat(pattern = "yyyyMMdd") DateTime to) {

        return new AvailabilityResourceAssembler().toModel(calendarService.findAvailability(id, new Interval(from, to), true));
    }

    @GetJson("/{id}")
    @ResponseBody
    public ResponseEntity<RepresentationModel> getAvailabilityResources(@PathVariable String id) {
        final RepresentationModel body = new AvailabilityResourceAssembler().collectionResource(id);
        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(body.getRequiredLink(AvailabilityResourceAssembler.REL_WEEK).getHref()));
        return new ResponseEntity<>(body, headers, HttpStatus.MULTIPLE_CHOICES);
    }

    @PostJson("/{id}")
    public ResponseEntity<AvailabilityResource> updateAvailability(@PathVariable String id, @RequestBody AvailabilityResource availabilityResource) {
        calendarService.updateAvailability(new AvailabilityResourceAssembler().fromResource(id, availabilityResource));

        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(new AvailabilityResourceAssembler().linkToAvailabilityResource(availabilityResource, id).toUri());
        return new ResponseEntity<>(headers, HttpStatus.CREATED);
    }

    @ExceptionHandler
    public ResponseEntity<Void> handleRuntimeException(RuntimeException e) {
        log.error("Failure in availability web API", e);
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    void setCalendarService(CalendarService calendarService) {
        this.calendarService = calendarService;
    }
}
